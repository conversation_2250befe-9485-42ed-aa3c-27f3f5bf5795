generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model UserLocation {
  id             String   @id @default(cuid())
  userId         String   @unique
  latitude       Float? // 纬度
  longitude      Float? // 经度
  lastUpdate     DateTime @db.DateTime(0)
  lastOnlineTime DateTime @db.DateTime(0)
  user           User     @relation(fields: [userId], references: [userId])

  @@index([userId], map: "UserLocation_userId_fkey")
}

model User {
  id                String             @id @default(cuid())
  userId            String             @unique
  passwordHash      String?
  nickname          String
  avatar            String?
  avatarURL         String? // 使用COS的URL存储
  lastActiveTime    DateTime?          @db.DateTime(0)
  createdAt         DateTime?          @db.DateTime(0)
  friendships       Friendship[]       @relation("UserFriendships")
  locations         UserLocation[]
  authoredCards     ItemCard[]         @relation("AuthoredCards") // 用户创建的卡片（作为作者）
  heldCards         UserItemCard[] // 用户持有的卡片（创建的 + 接收的）
  sharingLocation   Boolean            @default(true) // 用户可以选择是否共享位置，默认开启
  carbonCoins       Int                @default(0) // 用户的碳币数量
  sentTransfers     ItemCardTransfer[] @relation("SenderTransfers")
  receivedTransfers ItemCardTransfer[] @relation("ReceiverTransfers")
  ItemCard          ItemCard[]
  footprints        UserFootprints[] // 用户的足迹记录
  locationCheckIns  LocationCheckIns[] // 用户的地点打卡记录
}

model Friendship {
  id        String           @id @default(cuid())
  userId    String
  friendId  String
  status    FriendshipStatus
  createdAt DateTime         @db.DateTime(0)
  updatedAt DateTime         @db.DateTime(0)
  user      User             @relation("UserFriendships", fields: [userId], references: [userId])

  @@index([userId], map: "Friendship_userId_fkey")
}

enum FriendshipStatus {
  pending
  accepted
  rejected
}

enum TransferStatus {
  pending
  accepted
  rejected
}

enum ActivityType {
  walking
  cycling
  bus
  subway
}

model ItemCard {
  id            String             @id @default(cuid())
  tags          Json // 存储标签数组
  description   String             @db.Text
  title         String
  imageFileName String // 本地存储的文件名
  imageURL      String // COS返回的URL，供共享使用
  createdAt     DateTime           @default(now()) @db.DateTime(0)
  authorId      String // 存储创建这个卡片的用户id
  location      String // 位置字符串，用于显示
  latitude      Float? // 纬度
  longitude     Float? // 经度
  author        User               @relation("AuthoredCards", fields: [authorId], references: [userId])
  transfers     ItemCardTransfer[] // 卡片的传输记录
  holders       UserItemCard[] // 持有此卡片的用户关联
  User          User?              @relation(fields: [userId], references: [id])
  userId        String?

  @@index([authorId], map: "ItemCard_authorId_fkey")
  @@index([createdAt], map: "ItemCard_createdAt_idx")
}

model ItemCardTransfer {
  id           String         @id @default(cuid())
  senderId     String
  receiverId   String
  cardId       String
  transferTime DateTime       @default(now()) @db.DateTime(0)
  status       TransferStatus @default(pending)
  sender       User           @relation("SenderTransfers", fields: [senderId], references: [userId])
  receiver     User           @relation("ReceiverTransfers", fields: [receiverId], references: [userId])
  card         ItemCard       @relation(fields: [cardId], references: [id])

  @@index([senderId], map: "ItemCardTransfer_senderId_fkey")
  @@index([receiverId], map: "ItemCardTransfer_receiverId_fkey")
  @@index([cardId], map: "ItemCardTransfer_cardId_fkey")
  @@index([transferTime], map: "ItemCardTransfer_transferTime_idx")
}

model UserItemCard {
  id         String   @id @default(cuid())
  userId     String
  cardId     String
  remark     String?  @db.Text // 用户独立的备注（个性化）
  acquiredAt DateTime @default(now()) @db.DateTime(0) // 获取时间（创建或接受传输时）
  isOwner    Boolean  @default(false) // 是否是作者（true 为作者，便于区分编辑权限）
  user       User     @relation(fields: [userId], references: [userId])
  card       ItemCard @relation(fields: [cardId], references: [id])

  @@unique([userId, cardId]) // 防止用户重复持有同一卡片
  @@index([userId], map: "UserItemCard_userId_fkey")
  @@index([cardId], map: "UserItemCard_cardId_fkey")
  @@index([acquiredAt], map: "UserItemCard_acquiredAt_idx")
}

model UserFootprints {
  id            String       @id @default(cuid())
  userId        String
  user          User         @relation(fields: [userId], references: [userId])
  footPrints    Json // JSON 数组，包含 latitude, longitude, timestamp
  activityType  ActivityType // 出行活动类型
  isFinished    Boolean // 是否已完成此次出行
  totalDistance Float        @default(0) // 总距离（公里）
  createdAt     DateTime     @default(now()) @db.DateTime(0)
  updatedAt     DateTime     @updatedAt @db.DateTime(0)

  @@index([userId], map: "UserFootprints_userId_fkey")
  @@index([createdAt], map: "UserFootprints_createdAt_idx")
}

model LocationCheckIns {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [userId])
  position  String? // 地点名称，如"星巴克"
  latitude  Float // 纬度
  longitude Float // 经度
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  @@index([userId], map: "LocationCheckIns_userId_fkey")
  @@index([createdAt], map: "LocationCheckIns_createdAt_idx")
}
