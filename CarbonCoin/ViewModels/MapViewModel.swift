//
//  MapViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import Foundation
import SwiftUI
import MapKit
import CoreLocation

// MARK: - 地图样式类型枚举
enum MapStyleType: CaseIterable {
    case standard
    case hybrid

    var displayName: String {
        switch self {
        case .standard:
            return "标准"
        case .hybrid:
            return "混合"
        }
    }
}

// MARK: - 地图视图模型
@MainActor
class MapViewModel: ObservableObject {
    
    // MARK: - Published Properties
    /// 地图相机位置
    @Published var cameraPosition: MapCameraPosition
    
    /// 用户当前位置
    @Published var userLocation: CLLocationCoordinate2D?
    
    /// 地图区域
    @Published var mapRegion: MKCoordinateRegion
    
    /// 是否显示用户位置
    @Published var showUserLocation: Bool = true
    
    /// 记录用户的朝向
    @Published var userHeading: CLLocationDirection?
    
    /// 地图样式
    @Published var mapStyle: MapStyle = .standard(elevation: .flat, emphasis: .muted, pointsOfInterest: .including(.publicTransport), showsTraffic: false)

    /// 当前地图样式类型（用于切换）
    @Published var currentMapStyleType: MapStyleType = .standard
    
    /// 错误信息
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let locationManager = UserLocationManager()
    private let defaultLocation = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074) // 北京天安门
    private let defaultSpan = MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
    
    // MARK: - Initialization
    init() {
        // 初始化地图区域（默认为北京）
        let initialRegion = MKCoordinateRegion(
            center: defaultLocation,
            span: defaultSpan
        )
        self.mapRegion = initialRegion

        // 初始化相机位置
        self.cameraPosition = .region(initialRegion)
        

        // 监听位置管理器的状态变化
        setupLocationObservers()
    }
    
    // MARK: - Setup
    private func setupLocationObservers() {
        // 监听用户位置变化
        locationManager.$userLocation
            .receive(on: DispatchQueue.main)
            .sink { [weak self] location in
                self?.handleLocationUpdate(location)
            }
            .store(in: &cancellables)
        
        // 监听用户的朝向变化
        locationManager.$userHeading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] heading in
                self?.userHeading = heading
            }
            .store(in: &cancellables)
        
        // 监听位置错误
        locationManager.$locationError
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.errorMessage = error
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Public Methods
    
    /// 开始位置更新
    func startLocationUpdates() {
        locationManager.startLocationUpdates()
    }
    
    /// 停止位置更新
    func stopLocationUpdates() {
        locationManager.stopLocationUpdates()
    }
    
    /// 请求位置权限
    func requestLocationPermission() {
        locationManager.requestLocationPermission()
    }
    
    /// 移动到用户位置
    func moveToUserLocation() {
        guard let userLocation = userLocation else {
            requestLocationPermission()
            return
        }
        
        let region = MKCoordinateRegion(
            center: userLocation,
            span: defaultSpan
        )
        
        withAnimation(.easeInOut(duration: 1.0)) {
            mapRegion = region
            cameraPosition = .region(region)
        }
    }
    
    /// 移动到指定位置
    func moveToLocation(_ coordinate: CLLocationCoordinate2D, span: MKCoordinateSpan? = nil) {
        let region = MKCoordinateRegion(
            center: coordinate,
            span: span ?? defaultSpan
        )
        
        withAnimation(.easeInOut(duration: 1.0)) {
            mapRegion = region
            cameraPosition = .region(region)
        }
    }
    
    /// 缩放到指定级别
    func zoomToLevel(_ level: Double) {
        let span = MKCoordinateSpan(
            latitudeDelta: level,
            longitudeDelta: level
        )
        
        let center = userLocation ?? mapRegion.center
        let region = MKCoordinateRegion(center: center, span: span)
        
        withAnimation(.easeInOut(duration: 0.5)) {
            mapRegion = region
            cameraPosition = .region(region)
        }
    }
    
    /// 切换地图样式
    func toggleMapStyle() {
        switch currentMapStyleType {
        case .standard:
            currentMapStyleType = .hybrid
            mapStyle = .hybrid(elevation: .realistic, pointsOfInterest: .excludingAll, showsTraffic: false)
        case .hybrid:
            currentMapStyleType = .standard
            mapStyle = .standard(elevation: .flat, emphasis: .muted, pointsOfInterest: .excludingAll, showsTraffic: false)
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理位置更新
    private func handleLocationUpdate(_ location: CLLocationCoordinate2D?) {
        guard let location = location else { return }
        
        userLocation = location
        
        // 如果是首次获取位置，自动移动到用户位置
        if mapRegion.center.latitude == defaultLocation.latitude &&
           mapRegion.center.longitude == defaultLocation.longitude {
            moveToLocation(location)
        }
    }
}

// MARK: - Combine Import
import Combine
