//
//  PlaceCheckinViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation
import MapKit
import Combine

// MARK: - 地点打卡视图模型

/// 地点打卡视图模型，管理地点打卡功能的状态和业务逻辑
@MainActor
class PlaceCheckinViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 当前打卡记录列表
    @Published var checkinsList: [PlaceCheckin] = []

    /// 当前获取的位置信息
    @Published var currentLocationInfo: LocationInfo?

    /// 是否正在获取位置
    @Published var isGettingLocation: Bool = false

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 打卡统计信息
    @Published var checkinStats: PlaceCheckinStatsResponse?

    /// 用户编辑的位置名称
    @Published var customPositionName: String = ""

    /// 最后更新时间
    @Published var lastUpdateTime: Date?

    // MARK: - Private Properties

    private let checkinManager: CheckinManagerProtocol
    private let locationViewModel: LocationViewModel
    private let geocoder = CLGeocoder()
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init(checkinManager: CheckinManagerProtocol? = nil,
         locationViewModel: LocationViewModel? = nil) {
        self.checkinManager = checkinManager ?? CheckinManager()
        self.locationViewModel = locationViewModel ?? LocationViewModel()

        setupLocationUpdates()
    }


    // MARK: 获取当前位置信息
    func getCurrentLocation() async {
        print("📍 开始获取当前位置...")

        isGettingLocation = true
        errorMessage = nil

        do {
            // 获取当前位置
            let (location, locationString) = await locationViewModel.userLocationManager.getCurrentLocationInfo()

            guard let currentLocation = location else {
                errorMessage = "无法获取当前位置: \(locationString)"
                print("❌ 无法获取当前位置: \(locationString)")
                isGettingLocation = false
                return
            }

            print("📍 获取到位置: \(currentLocation.coordinate)")
            print("📍 位置精度: \(currentLocation.horizontalAccuracy)m")

            // 检查位置精度
            guard currentLocation.horizontalAccuracy < 100.0 else {
                errorMessage = "位置精度不足，请稍后重试"
                print("❌ 位置精度不足: \(currentLocation.horizontalAccuracy)m")
                isGettingLocation = false
                return
            }

            // 执行地理编码
            let (address, placeName) = await performGeocoding(location: currentLocation)

            // 创建位置信息
            let locationInfo = LocationInfo(
                location: currentLocation,
                address: address,
                placeName: placeName
            )

            currentLocationInfo = locationInfo

            // 自动填充位置名称
            customPositionName = locationInfo.bestDisplayName

            print("✅ 位置获取成功")
            print("📍 地址: \(address ?? "未知")")
            print("📍 地点: \(placeName ?? "未知")")

        } catch {
            errorMessage = "获取位置失败: \(error.localizedDescription)"
            print("❌ 获取位置失败: \(error.localizedDescription)")
        }

        isGettingLocation = false
    }

    // MARK: 创建地点打卡记录
    /// - Parameter userId: 用户ID
    func createCheckin(userId: String) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        guard let locationInfo = currentLocationInfo else {
            errorMessage = "请先获取位置信息"
            return
        }

        print("📍 创建地点打卡记录，用户ID: \(userId)")

        isLoading = true
        errorMessage = nil

        do {
            // 使用用户编辑的位置名称，如果为空则使用自动获取的名称
            let finalPositionName = customPositionName.trimmingCharacters(in: .whitespacesAndNewlines)
            let positionToUse = finalPositionName.isEmpty ? nil : finalPositionName

            let checkin = try await checkinManager.createPlaceCheckin(
                userId: userId,
                position: positionToUse,
                latitude: locationInfo.latitude,
                longitude: locationInfo.longitude
            )

            // 添加到本地列表
            checkinsList.insert(checkin, at: 0)
            lastUpdateTime = Date()

            // 重置状态
            currentLocationInfo = nil
            customPositionName = ""

            print("✅ 成功创建地点打卡记录: \(checkin.id)")
            print("📍 位置: \(checkin.displayName)")

        } catch {
            errorMessage = error.localizedDescription
            print("❌ 创建地点打卡记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 查询地点打卡记录列表
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    func loadCheckinsList(userId: String, startDate: Date? = nil, endDate: Date? = nil) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let checkins = try await checkinManager.queryPlaceCheckins(
                userId: userId,
                startDate: startDate,
                endDate: endDate
            )
            checkinsList = checkins
            lastUpdateTime = Date()
            print("✅ 成功加载地点打卡记录: \(checkins.count) 条")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 加载地点打卡记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 查询地点打卡统计
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    func loadCheckinStats(userId: String, startDate: Date? = nil, endDate: Date? = nil) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let stats = try await checkinManager.queryPlaceCheckinStats(
                userId: userId,
                startDate: startDate,
                endDate: endDate
            )
            checkinStats = stats
            print("✅ 成功加载地点打卡统计")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 加载地点打卡统计失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 更新地点打卡记录
    /// - Parameters:
    ///   - id: 打卡记录ID
    ///   - position: 新的位置名称
    ///   - latitude: 新的纬度
    ///   - longitude: 新的经度
    func updateCheckin(id: String, position: String? = nil, latitude: Double? = nil, longitude: Double? = nil) async {
        guard !id.isEmpty else {
            errorMessage = "打卡记录ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let updatedCheckin = try await checkinManager.updatePlaceCheckin(
                id: id,
                position: position,
                latitude: latitude,
                longitude: longitude
            )

            // 更新本地列表
            if let index = checkinsList.firstIndex(where: { $0.id == updatedCheckin.id }) {
                checkinsList[index] = updatedCheckin
            }

            lastUpdateTime = Date()
            print("✅ 成功更新地点打卡记录: \(updatedCheckin.id)")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 更新地点打卡记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 删除地点打卡记录
    /// - Parameter id: 打卡记录ID
    func deleteCheckin(id: String) async {
        guard !id.isEmpty else {
            errorMessage = "打卡记录ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let success = try await checkinManager.deletePlaceCheckin(id: id)
            if success {
                // 从本地列表中移除
                checkinsList.removeAll { $0.id == id }
                lastUpdateTime = Date()
                print("✅ 成功删除地点打卡记录: \(id)")
            }
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 删除地点打卡记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    /// 刷新位置信息（重新获取）
    func refreshLocation() async {
        await getCurrentLocation()
    }

    /// 重置位置信息
    func resetLocationInfo() {
        currentLocationInfo = nil
        customPositionName = ""
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: 获取打卡记录的地图标注
    /// - Parameter checkin: 打卡记录
    /// - Returns: 地图标注
    func getMapAnnotation(for checkin: PlaceCheckin) -> MKPointAnnotation {
        let annotation = MKPointAnnotation()
        annotation.coordinate = checkin.coordinate
        annotation.title = checkin.displayName
        annotation.subtitle = checkin.formattedCreatedAt
        return annotation
    }

    /// 获取所有打卡记录的地图标注
    /// - Returns: 地图标注数组
    func getAllMapAnnotations() -> [MKPointAnnotation] {
        return checkinsList.map { getMapAnnotation(for: $0) }
    }

    /// 获取当前位置的地图标注
    /// - Returns: 当前位置的地图标注
    func getCurrentLocationAnnotation() -> MKPointAnnotation? {
        guard let locationInfo = currentLocationInfo else { return nil }

        let annotation = MKPointAnnotation()
        annotation.coordinate = locationInfo.coordinate
        annotation.title = "当前位置"
        annotation.subtitle = locationInfo.bestDisplayName
        return annotation
    }

    // MARK: - Private Methods

    /// 设置位置更新监听
    private func setupLocationUpdates() {
        // 监听位置管理器的位置更新
        locationViewModel.$currentUserLocation
            .compactMap { $0 }
            .sink { [weak self] userLocationInfo in
                // 这里可以根据需要处理位置更新
                print("📍 位置更新: \(userLocationInfo.clLocation.coordinate)")
            }
            .store(in: &cancellables)
    }

    /// 执行地理编码
    /// - Parameter location: 位置
    /// - Returns: 地址和地点名称
    private func performGeocoding(location: CLLocation) async -> (address: String?, placeName: String?) {
        print("🌍 开始地理编码...")

        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(location)

            guard let placemark = placemarks.first else {
                print("❌ 地理编码未返回结果")
                return (nil, nil)
            }

            // 构建地址字符串
            var addressComponents: [String] = []

            if let country = placemark.country {
                addressComponents.append(country)
            }
            if let administrativeArea = placemark.administrativeArea {
                addressComponents.append(administrativeArea)
            }
            if let locality = placemark.locality {
                addressComponents.append(locality)
            }
            if let subLocality = placemark.subLocality {
                addressComponents.append(subLocality)
            }
            if let thoroughfare = placemark.thoroughfare {
                addressComponents.append(thoroughfare)
            }
            if let subThoroughfare = placemark.subThoroughfare {
                addressComponents.append(subThoroughfare)
            }

            let address = addressComponents.joined(separator: ", ")

            // 获取地点名称（优先使用name，然后是thoroughfare）
            let placeName = placemark.name ?? placemark.thoroughfare

            print("✅ 地理编码成功")
            print("🌍 地址: \(address)")
            print("🌍 地点: \(placeName ?? "未知")")

            return (address.isEmpty ? nil : address, placeName)

        } catch {
            print("❌ 地理编码失败: \(error.localizedDescription)")
            return (nil, nil)
        }
    }
}

// MARK: - Deinitializer

extension PlaceCheckinViewModel {
    /// 清理资源
    func cleanup() {
        cancellables.removeAll()
        geocoder.cancelGeocode()
    }
}
