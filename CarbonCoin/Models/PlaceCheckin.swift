//
//  PlaceCheckin.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation
import MapKit

// MARK: - 地点打卡记录模型

/// 地点打卡记录
struct PlaceCheckin: Codable, Identifiable, Equatable {
    let id: String              // 打卡记录ID
    let userId: String          // 用户ID
    let position: String?       // 地点名称
    let latitude: Double        // 纬度
    let longitude: Double       // 经度
    let createdAt: Date         // 创建时间
    let updatedAt: Date         // 更新时间

    /// 转换为CLLocation
    var clLocation: CLLocation {
        return CLLocation(latitude: latitude, longitude: longitude)
    }

    /// 转换为CLLocationCoordinate2D
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }

    /// 相对时间显示
    var relativeTimeString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }

    /// 显示名称（优先使用position，否则显示坐标）
    var displayName: String {
        return position ?? "(\(String(format: "%.4f", latitude)), \(String(format: "%.4f", longitude)))"
    }
}

// MARK: - 位置信息模型

/// 位置信息（用于位置获取和地理编码）
struct LocationInfo: Codable, Equatable {
    let latitude: Double        // 纬度
    let longitude: Double       // 经度
    let address: String?        // 地址字符串
    let placeName: String?      // 地点名称
    let accuracy: Double?       // 位置精度
    let timestamp: Date         // 获取时间

    /// 从CLLocation创建位置信息
    init(location: CLLocation, address: String? = nil, placeName: String? = nil) {
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.address = address
        self.placeName = placeName
        self.accuracy = location.horizontalAccuracy
        self.timestamp = location.timestamp
    }

    /// 手动创建位置信息
    init(latitude: Double, longitude: Double, address: String? = nil, placeName: String? = nil, accuracy: Double? = nil, timestamp: Date = Date()) {
        self.latitude = latitude
        self.longitude = longitude
        self.address = address
        self.placeName = placeName
        self.accuracy = accuracy
        self.timestamp = timestamp
    }

    /// 转换为CLLocation
    var clLocation: CLLocation {
        return CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: 0,
            horizontalAccuracy: accuracy ?? 0,
            verticalAccuracy: 0,
            timestamp: timestamp
        )
    }

    /// 转换为CLLocationCoordinate2D
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    /// 最佳显示名称
    var bestDisplayName: String {
        return placeName ?? address ?? "(\(String(format: "%.4f", latitude)), \(String(format: "%.4f", longitude)))"
    }

    /// 格式化的精度信息
    var formattedAccuracy: String {
        guard let accuracy = accuracy else { return "未知精度" }
        return String(format: "精度: %.0f米", accuracy)
    }
}

// MARK: - API请求模型

/// 创建地点打卡记录请求
struct CreatePlaceCheckinRequest: Codable {
    let userId: String
    let position: String?
    let latitude: Double
    let longitude: Double

    init(userId: String, position: String? = nil, latitude: Double, longitude: Double) {
        self.userId = userId
        self.position = position
        self.latitude = latitude
        self.longitude = longitude
    }

    init(userId: String, locationInfo: LocationInfo, customPosition: String? = nil) {
        self.userId = userId
        self.position = customPosition ?? locationInfo.placeName ?? locationInfo.address
        self.latitude = locationInfo.latitude
        self.longitude = locationInfo.longitude
    }
}

/// 更新地点打卡记录请求
struct UpdatePlaceCheckinRequest: Codable {
    let id: String
    let position: String?
    let latitude: Double?
    let longitude: Double?

    init(id: String, position: String? = nil, latitude: Double? = nil, longitude: Double? = nil) {
        self.id = id
        self.position = position
        self.latitude = latitude
        self.longitude = longitude
    }
}

/// 删除地点打卡记录请求
struct DeletePlaceCheckinRequest: Codable {
    let id: String
}

/// 查询地点打卡记录详情请求
struct PlaceCheckinDetailRequest: Codable {
    let id: String
}

/// 查询地点打卡统计请求
struct PlaceCheckinStatsRequest: Codable {
    let userId: String
    let startDate: String?
    let endDate: String?

    init(userId: String, startDate: Date? = nil, endDate: Date? = nil) {
        self.userId = userId
        self.startDate = startDate?.ISO8601Format()
        self.endDate = endDate?.ISO8601Format()
    }
}

// MARK: - API响应模型

/// 地点打卡API响应基础模型
struct PlaceCheckinResponse<T: Codable>: Codable {
    let success: Bool
    let message: String?
    let data: T?
    let error: String?
}

/// 地点打卡记录详情响应（包含用户信息）
struct PlaceCheckinDetailResponse: Codable {
    let id: String
    let userId: String
    let position: String?
    let latitude: Double
    let longitude: Double
    let createdAt: Date
    let updatedAt: Date
    let user: UserInfo

    struct UserInfo: Codable {
        let userId: String
        let nickname: String
        let avatar: String?
        let avatarURL: String?
    }

    /// 转换为PlaceCheckin模型
    func toPlaceCheckin() -> PlaceCheckin {
        return PlaceCheckin(
            id: id,
            userId: userId,
            position: position,
            latitude: latitude,
            longitude: longitude,
            createdAt: createdAt,
            updatedAt: updatedAt
        )
    }
}

/// 地点打卡统计响应
struct PlaceCheckinStatsResponse: Codable {
    let totalCheckIns: Int              // 总打卡次数
    let uniquePositionsCount: Int       // 不同地点的个数
    let positionStats: [PositionStat]   // 按地点分组的统计
    let timeRange: TimeRange            // 时间范围

    struct PositionStat: Codable {
        let position: String    // 地点名称
        let count: Int         // 打卡次数
    }

    struct TimeRange: Codable {
        let startDate: String?
        let endDate: String?
    }

    /// 最热门的地点
    var topPosition: PositionStat? {
        return positionStats.first
    }

    /// 格式化的统计摘要
    var summaryText: String {
        return "总共在 \(uniquePositionsCount) 个不同地点打卡 \(totalCheckIns) 次"
    }
}

// MARK: - 地点打卡错误类型

/// 地点打卡服务错误枚举
enum PlaceCheckinError: Error, LocalizedError {
    case networkError(Error)
    case serverError(String)
    case decodingError(Error)
    case missingUserId
    case missingCheckinId
    case invalidCoordinates
    case checkinNotFound
    case userNotFound
    case locationServiceError
    case geocodingError
    case permissionDenied
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .decodingError(let error):
            return "数据解析失败: \(error.localizedDescription)"
        case .missingUserId:
            return "缺少用户ID"
        case .missingCheckinId:
            return "缺少打卡记录ID"
        case .invalidCoordinates:
            return "无效的坐标信息"
        case .checkinNotFound:
            return "打卡记录不存在"
        case .userNotFound:
            return "用户不存在"
        case .locationServiceError:
            return "位置服务错误"
        case .geocodingError:
            return "地理编码失败"
        case .permissionDenied:
            return "位置权限被拒绝"
        case .unknown(let message):
            return message
        }
    }
}
