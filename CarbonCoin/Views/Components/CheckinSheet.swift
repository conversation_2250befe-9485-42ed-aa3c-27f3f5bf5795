//
//  CheckinSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import SwiftUI
import MapKit

// MARK: - 地点打卡 Sheet 组件

/// 地点打卡 Sheet 视图，包含打卡功能和记录查看两个 Tab
struct CheckinSheet: View {
    @StateObject private var placeCheckinViewModel = PlaceCheckinViewModel()
    @EnvironmentObject private var appSettings: AppSettings
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态变量
    @State private var selectedTab = 0
    @State private var showCheckinAnnotations = true
    @State private var searchText = ""
    @State private var startDate: Date?
    @State private var endDate: Date?
    @State private var showDatePicker = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // MARK: 顶部标题栏
                HStack {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(.brandGreen)

                    Spacer()

                    Text("地点打卡")
                        .font(.title2Brand)
                        .foregroundColor(.primary)

                    Spacer()

                    // 占位按钮保持对称
                    Button("") { }
                        .disabled(true)
                        .opacity(0)
                }
                .padding(.horizontal, Theme.Spacing.lg)
                .padding(.vertical, Theme.Spacing.md)
                .background(.ultraThinMaterial)

                // MARK: Tab 选择器
                Picker("选择功能", selection: $selectedTab) {
                    Text("打卡").tag(0)
                    Text("记录").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, Theme.Spacing.lg)
                .padding(.vertical, Theme.Spacing.sm)
                .background(.ultraThinMaterial)

                // MARK: Tab 内容
                TabView(selection: $selectedTab) {
                    // Tab 1 - 打卡功能
                    CheckinTabView(
                        placeCheckinViewModel: placeCheckinViewModel,
                        showCheckinAnnotations: $showCheckinAnnotations,
                        appSettings: appSettings
                    )
                    .tag(0)

                    // Tab 2 - 记录查看
                    RecordsTabView(
                        placeCheckinViewModel: placeCheckinViewModel,
                        searchText: $searchText,
                        startDate: $startDate,
                        endDate: $endDate,
                        showDatePicker: $showDatePicker,
                        appSettings: appSettings
                    )
                    .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .background(.regularMaterial)
        }
        .presentationDetents([.fraction(0.5)])
        .presentationDragIndicator(.visible)
        .onAppear {
            // 加载打卡记录
            Task {
                await placeCheckinViewModel.loadCheckinsList(userId: appSettings.userId)
            }
        }
    }
}

// MARK: - 打卡功能 Tab 视图

private struct CheckinTabView: View {
    @ObservedObject var placeCheckinViewModel: PlaceCheckinViewModel
    @Binding var showCheckinAnnotations: Bool
    let appSettings: AppSettings

    var body: some View {
        ScrollView {
            VStack(spacing: Theme.Spacing.lg) {
                // MARK: 当前位置打卡区域
                VStack(spacing: Theme.Spacing.md) {
                    Text("当前位置打卡")
                        .font(.headline)
                        .foregroundColor(.primary)

                    if let locationInfo = placeCheckinViewModel.currentLocationInfo {
                        // 显示获取到的位置信息
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            HStack {
                                Image(systemName: "location.fill")
                                    .foregroundColor(.brandGreen)
                                Text("位置信息")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                Spacer()
                            }

                            Text(locationInfo.bestDisplayName)
                                .font(.body)
                                .foregroundColor(.secondary)

                            Text(locationInfo.formattedAccuracy)
                                .font(.caption)
                                .foregroundColor(.tertiary)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)

                        // 用户可编辑的位置名称
                        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                            Text("地点名称（可编辑）")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            TextField("输入地点名称", text: $placeCheckinViewModel.customPositionName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }

                        // 确认打卡按钮
                        Button(action: {
                            Task {
                                await placeCheckinViewModel.createCheckin(userId: appSettings.userId)
                            }
                        }) {
                            HStack {
                                if placeCheckinViewModel.isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image("log-checkin")
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                }
                                Text("确认打卡")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.brandGreen)
                            .cornerRadius(12)
                        }
                        .disabled(placeCheckinViewModel.isLoading)

                    } else {
                        // 获取位置按钮
                        Button(action: {
                            Task {
                                await placeCheckinViewModel.getCurrentLocation()
                            }
                        }) {
                            HStack {
                                if placeCheckinViewModel.isGettingLocation {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "location.circle")
                                        .font(.title2)
                                }
                                Text("获取当前位置")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.brandGreen)
                            .cornerRadius(12)
                        }
                        .disabled(placeCheckinViewModel.isGettingLocation)
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(16)

                // MARK: 显示设置区域
                VStack(spacing: Theme.Spacing.md) {
                    Text("显示设置")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Toggle("在地图上显示打卡点", isOn: $showCheckinAnnotations)
                        .toggleStyle(SwitchToggleStyle(tint: .brandGreen))
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(16)

                // MARK: 错误信息显示
                if let errorMessage = placeCheckinViewModel.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
            }
            .padding(Theme.Spacing.lg)
        }
    }
}

// MARK: - 记录查看 Tab 视图

private struct RecordsTabView: View {
    @ObservedObject var placeCheckinViewModel: PlaceCheckinViewModel
    @Binding var searchText: String
    @Binding var startDate: Date?
    @Binding var endDate: Date?
    @Binding var showDatePicker: Bool
    let appSettings: AppSettings

    // 过滤后的打卡记录
    private var filteredCheckins: [PlaceCheckin] {
        var checkins = placeCheckinViewModel.checkinsList

        // 按搜索文本过滤
        if !searchText.isEmpty {
            checkins = checkins.filter { checkin in
                checkin.displayName.localizedCaseInsensitiveContains(searchText)
            }
        }

        // 按时间区间过滤
        if let start = startDate {
            checkins = checkins.filter { $0.createdAt >= start }
        }
        if let end = endDate {
            checkins = checkins.filter { $0.createdAt <= end }
        }

        return checkins
    }

    var body: some View {
        VStack(spacing: 0) {
            // MARK: 搜索和筛选区域
            VStack(spacing: Theme.Spacing.sm) {
                // 搜索框
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    TextField("搜索地点名称", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)

                // 时间筛选按钮
                Button(action: {
                    showDatePicker.toggle()
                }) {
                    HStack {
                        Image(systemName: "calendar")
                        Text("时间筛选")
                        Spacer()
                        if startDate != nil || endDate != nil {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.brandGreen)
                        }
                    }
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)
                }
                .foregroundColor(.primary)
            }
            .padding(Theme.Spacing.lg)
            .background(.ultraThinMaterial)

            // MARK: 记录列表
            if filteredCheckins.isEmpty {
                VStack(spacing: Theme.Spacing.md) {
                    Image(systemName: "location.slash")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    Text("暂无打卡记录")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    if !searchText.isEmpty || startDate != nil || endDate != nil {
                        Text("尝试调整搜索条件")
                            .font(.caption)
                            .foregroundColor(.tertiary)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(.regularMaterial)
            } else {
                List(filteredCheckins) { checkin in
                    CheckinRecordRow(checkin: checkin)
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                }
                .listStyle(PlainListStyle())
                .background(.regularMaterial)
            }
        }
        .sheet(isPresented: $showDatePicker) {
            DateFilterSheet(
                startDate: $startDate,
                endDate: $endDate,
                onApply: {
                    Task {
                        await placeCheckinViewModel.loadCheckinsList(
                            userId: appSettings.userId,
                            startDate: startDate,
                            endDate: endDate
                        )
                    }
                }
            )
        }
    }
}

// MARK: - 打卡记录行视图

private struct CheckinRecordRow: View {
    let checkin: PlaceCheckin

    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 打卡图标
            Image("log-checkin")
                .resizable()
                .frame(width: 24, height: 24)
                .foregroundColor(.brandGreen)
                .padding(8)
                .background(Color.brandGreen.opacity(0.1))
                .clipShape(Circle())

            // 打卡信息
            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text(checkin.displayName)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)

                Text(checkin.formattedCreatedAt)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("(\(String(format: "%.4f", checkin.latitude)), \(String(format: "%.4f", checkin.longitude)))")
                    .font(.caption2)
                    .foregroundColor(.tertiary)
            }

            Spacer()

            // 相对时间
            Text(checkin.relativeTimeString)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.vertical, Theme.Spacing.xs)
    }
}

// MARK: - 日期筛选 Sheet

private struct DateFilterSheet: View {
    @Binding var startDate: Date?
    @Binding var endDate: Date?
    let onApply: () -> Void
    @Environment(\.dismiss) private var dismiss

    @State private var tempStartDate: Date = Date()
    @State private var tempEndDate: Date = Date()
    @State private var useStartDate = false
    @State private var useEndDate = false

    var body: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 开始日期
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    Toggle("设置开始日期", isOn: $useStartDate)
                        .toggleStyle(SwitchToggleStyle(tint: .brandGreen))

                    if useStartDate {
                        DatePicker("开始日期", selection: $tempStartDate, displayedComponents: .date)
                            .datePickerStyle(WheelDatePickerStyle())
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(12)

                // 结束日期
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    Toggle("设置结束日期", isOn: $useEndDate)
                        .toggleStyle(SwitchToggleStyle(tint: .brandGreen))

                    if useEndDate {
                        DatePicker("结束日期", selection: $tempEndDate, displayedComponents: .date)
                            .datePickerStyle(WheelDatePickerStyle())
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .cornerRadius(12)

                Spacer()
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("时间筛选")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        startDate = useStartDate ? tempStartDate : nil
                        endDate = useEndDate ? tempEndDate : nil
                        onApply()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            if let start = startDate {
                tempStartDate = start
                useStartDate = true
            }
            if let end = endDate {
                tempEndDate = end
                useEndDate = true
            }
        }
    }
}

// MARK: - Preview

#Preview {
    @Previewable @State var appSettings = AppSettings()
    CheckinSheet()
        .environmentObject(appSettings)
}
