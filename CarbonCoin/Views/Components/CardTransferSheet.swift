//
//  CardTransferSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import SwiftUI

/// 卡片传输记录展示Sheet
struct CardTransferSheet: View {
    @StateObject private var viewModel = CardTransferViewModel()
    @State private var selectedTab: TransferTab = .pending
    @Environment(\.dismiss) private var dismiss

    /// 传输标签页枚举
    enum TransferTab: String, CaseIterable {
        case pending = "待处理"
        case accepted = "已完成"
        case rejected = "已拒绝"

        var systemImage: String {
            switch self {
            case .pending:
                return "clock.circle"
            case .accepted:
                return "checkmark.circle"
            case .rejected:
                return "xmark.circle"
            }
        }

        var color: Color {
            switch self {
            case .pending:
                return .orange
            case .accepted:
                return .green
            case .rejected:
                return .red
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签页选择器
                tabSelector

                // 内容区域
                contentView
            }
            .navigationTitle("卡片传输")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("刷新") {
                        Task {
                            await viewModel.refreshAllTransfers()
                        }
                    }
                    .disabled(viewModel.isLoading)
                }
            }
        }
        .task {
            await viewModel.refreshAllTransfers()
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.clearMessages()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("成功", isPresented: .constant(viewModel.successMessage != nil)) {
            Button("确定") {
                viewModel.clearMessages()
            }
        } message: {
            if let successMessage = viewModel.successMessage {
                Text(successMessage)
            }
        }
    }

    // MARK: - 标签页选择器

    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(TransferTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.systemImage)
                            .font(.title3)
                            .foregroundColor(selectedTab == tab ? tab.color : .gray)

                        Text(tab.rawValue)
                            .font(.caption)
                            .foregroundColor(selectedTab == tab ? tab.color : .gray)

                        // 数量徽章
                        if getTransferCount(for: tab) > 0 {
                            Text("\(getTransferCount(for: tab))")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(tab.color)
                                .clipShape(Capsule())
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        selectedTab == tab ? tab.color.opacity(0.1) : Color.clear
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(Color(.systemGray6))
        .cornerRadius(Theme.CornerRadius.md)
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.top, Theme.Spacing.sm)
    }

    // MARK: - 内容视图

    private var contentView: some View {
        Group {
            if viewModel.isLoading {
                loadingView
            } else {
                transferListView
            }
        }
    }

    private var loadingView: some View {
        VStack(spacing: Theme.Spacing.md) {
            ProgressView()
                .scaleEffect(1.2)

            Text("加载传输记录...")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var transferListView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: Theme.Spacing.sm),
                GridItem(.flexible(), spacing: Theme.Spacing.sm)
            ], spacing: Theme.Spacing.md) {
                ForEach(getCurrentTransfers(), id: \.id) { transfer in
                    CardTransferItemView(
                        transfer: transfer,
                        onAccept: { transferId in
                            Task {
                                await viewModel.processCardTransfer(transferId: transferId, action: "accept")
                            }
                        },
                        onReject: { transferId in
                            Task {
                                await viewModel.processCardTransfer(transferId: transferId, action: "reject")
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
            .padding(.top, Theme.Spacing.md)
        }
    }

    // MARK: - Helper Methods

    /// 获取指定标签页的传输数量
    private func getTransferCount(for tab: TransferTab) -> Int {
        switch tab {
        case .pending:
            return viewModel.pendingReceivedTransfers.count
        case .accepted:
            return viewModel.acceptedReceivedTransfers.count + viewModel.acceptedSentTransfers.count
        case .rejected:
            return viewModel.rejectedReceivedTransfers.count + viewModel.rejectedSentTransfers.count
        }
    }

    /// 获取当前标签页的传输记录
    private func getCurrentTransfers() -> [CardTransferRecord] {
        switch selectedTab {
        case .pending:
            return viewModel.pendingReceivedTransfers
        case .accepted:
            return (viewModel.acceptedReceivedTransfers + viewModel.acceptedSentTransfers)
                .sorted { $0.transferTime > $1.transferTime }
        case .rejected:
            return (viewModel.rejectedReceivedTransfers + viewModel.rejectedSentTransfers)
                .sorted { $0.transferTime > $1.transferTime }
        }
    }
}

// MARK: - 卡片传输项视图

/// 单个卡片传输记录的展示视图
struct CardTransferItemView: View {
    let transfer: CardTransferRecord
    let onAccept: (String) -> Void
    let onReject: (String) -> Void

    @AppStorage("currentUserId") private var currentUserId: String = ""

    var body: some View {
        NavigationLink(destination: CardTransferDetailView(transfer: transfer)) {
            cardContent
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var cardContent: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            // 卡片图片
            cardImage

            // 卡片标题
            cardTitle

            // 传输信息
            transferInfo

            // 操作按钮（仅待处理的接收传输显示）
            if transfer.status == .pending && transfer.receiverId == currentUserId {
                actionButtons
            }
        }
        .padding(Theme.Spacing.sm)
        .background(Color.cardBackground.opacity(0.1))
        .cornerRadius(Theme.CornerRadius.lg)
        .glassCard()
    }

    private var cardImage: some View {
        Group {
            if let card = transfer.card, !card.imageURL.isEmpty {
                AsyncImage(url: URL(string: card.imageURL)) { image in
                    image
                        .resizable()
                        .scaledToFill()
                } placeholder: {
                    Rectangle()
                        .fill(Color.cardBackground.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                }
                .frame(height: 100)
                .clipped()
                .cornerRadius(Theme.CornerRadius.md)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 100)
                    .cornerRadius(Theme.CornerRadius.md)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.textSecondary)
                    )
            }
        }
    }

    private var cardTitle: some View {
        Text(transfer.card?.title ?? "未知卡片")
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    private var transferInfo: some View {
        VStack(alignment: .leading, spacing: 2) {
            // 传输方向和用户信息
            HStack {
                Image(systemName: transfer.senderId == currentUserId ? "arrow.up.circle" : "arrow.down.circle")
                    .foregroundColor(transfer.senderId == currentUserId ? .blue : .green)

                Text(transfer.senderId == currentUserId ? "发送给" : "来自")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)

                Text(getOtherUserName())
                    .font(.caption2)
                    .foregroundColor(.textPrimary)
                    .lineLimit(1)
            }

            // 传输时间
            Text(transfer.formattedTransferTime)
                .font(.caption2)
                .foregroundColor(.textSecondary)

            // 状态标签
            statusBadge
        }
    }

    private var statusBadge: some View {
        HStack {
            Circle()
                .fill(transfer.status.color)
                .frame(width: 6, height: 6)

            Text(transfer.status.localizedDescription)
                .font(.caption2)
                .foregroundColor(transfer.status.color)
        }
    }

    private var actionButtons: some View {
        HStack(spacing: Theme.Spacing.xs) {
            Button("拒绝") {
                onReject(transfer.id)
            }
            .font(.caption)
            .foregroundColor(.red)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.red.opacity(0.1))
            .cornerRadius(Theme.CornerRadius.sm)

            Button("接受") {
                onAccept(transfer.id)
            }
            .font(.caption)
            .foregroundColor(.green)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.green.opacity(0.1))
            .cornerRadius(Theme.CornerRadius.sm)
        }
    }

    // MARK: - Helper Methods

    private func getOtherUserName() -> String {
        if transfer.senderId == currentUserId {
            return transfer.receiver?.nickname ?? "未知用户"
        } else {
            return transfer.sender?.nickname ?? "未知用户"
        }
    }
}

// MARK: - 扩展

extension CardTransferStatus {
    var color: Color {
        switch self {
        case .pending:
            return .orange
        case .accepted:
            return .green
        case .rejected:
            return .red
        }
    }
}

// MARK: - 卡片传输详情视图

/// 卡片传输详情页面
struct CardTransferDetailView: View {
    let transfer: CardTransferRecord
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                // 卡片信息
                if let card = transfer.card {
                    cardSection(card)
                }

                // 传输信息
                transferSection

                // 用户信息
                userSection
            }
            .padding(Theme.Spacing.md)
        }
        .navigationTitle("传输详情")
        .navigationBarTitleDisplayMode(.inline)
    }

    private func cardSection(_ card: CardInfo) -> some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("卡片信息")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                // 卡片图片
                if !card.imageURL.isEmpty {
                    AsyncImage(url: URL(string: card.imageURL)) { image in
                        image
                            .resizable()
                            .scaledToFit()
                    } placeholder: {
                        Rectangle()
                            .fill(Color.cardBackground.opacity(0.3))
                            .overlay(ProgressView())
                    }
                    .frame(maxHeight: 200)
                    .cornerRadius(Theme.CornerRadius.lg)
                }

                Text(card.title)
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                Text(card.description)
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)

                // 标签
                if !card.tags.isEmpty {
                    HStack {
                        ForEach(card.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .foregroundColor(.textPrimary)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.brandGreen.opacity(0.2))
                                .cornerRadius(Theme.CornerRadius.sm)
                        }
                    }
                }
            }
            .padding(Theme.Spacing.md)
            .background(Color.cardBackground.opacity(0.1))
            .cornerRadius(Theme.CornerRadius.lg)
        }
    }

    private var transferSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("传输信息")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                HStack {
                    Text("状态:")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    HStack {
                        Circle()
                            .fill(transfer.status.color)
                            .frame(width: 8, height: 8)

                        Text(transfer.status.localizedDescription)
                            .font(.bodyBrand)
                            .foregroundColor(transfer.status.color)
                    }
                }

                HStack {
                    Text("传输时间:")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Text(transfer.formattedTransferTime)
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                }
            }
            .padding(Theme.Spacing.md)
            .background(Color.cardBackground.opacity(0.1))
            .cornerRadius(Theme.CornerRadius.lg)
        }
    }

    private var userSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("参与用户")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                // 发送者信息
                if let sender = transfer.sender {
                    userInfoRow(title: "发送者", user: sender, isReceiver: false)
                }

                // 接收者信息
                if let receiver = transfer.receiver {
                    userInfoRow(title: "接收者", user: receiver, isReceiver: true)
                }
            }
        }
    }

    private func userInfoRow(title: String, user: UserInfo, isReceiver: Bool) -> some View {
        HStack {
            Image(systemName: isReceiver ? "arrow.down.circle" : "arrow.up.circle")
                .foregroundColor(isReceiver ? .green : .blue)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.textSecondary)

                Text(user.nickname)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
            }

            Spacer()
        }
        .padding(Theme.Spacing.md)
        .background(Color.cardBackground.opacity(0.1))
        .cornerRadius(Theme.CornerRadius.lg)
    }
}

// MARK: - 预览

#Preview {
    CardTransferSheet()
}
