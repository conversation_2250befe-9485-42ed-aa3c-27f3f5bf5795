//
//  FootprintsManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation

// MARK: - 足迹管理器协议

/// 足迹管理器协议，定义足迹相关的API接口
protocol FootprintsManagerProtocol {
    /// 创建足迹记录
    func createFootprints(userId: String, activityType: ActivityType, isFinished: Bool, footPrints: [FootprintPoint]) async throws -> UserFootprints

    /// 查询足迹记录
    func queryFootprints(userId: String, startDate: Date?, endDate: Date?, activityType: ActivityType?) async throws -> [UserFootprints]

    /// 更新足迹记录
    func updateFootprints(footprintId: String, footPrints: [FootprintPoint]?, activityType: ActivityType?, isFinished: Bool?) async throws -> UserFootprints

    /// 删除足迹记录
    func deleteFootprints(footprintId: String) async throws -> Bool

    /// 查询足迹记录详情
    func queryFootprintsDetail(footprintId: String) async throws -> FootprintsDetailResponse

    /// 查询足迹统计
    func queryFootprintsStats(userId: String, startDate: Date?, endDate: Date?, activityType: ActivityType?) async throws -> FootprintsStatsResponse
}

// MARK: - 足迹管理器

/// 足迹管理器，负责处理足迹相关的网络请求
@MainActor
class FootprintsManager: ObservableObject, FootprintsManagerProtocol {

    // MARK: - Published Properties

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateCoding()
    }

    // MARK: - Public Methods

    // MARK: 创建足迹记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - activityType: 活动类型
    ///   - isFinished: 是否已完成
    ///   - footPrints: 足迹点数组
    /// - Returns: 创建的足迹记录
    func createFootprints(userId: String, activityType: ActivityType, isFinished: Bool = false, footPrints: [FootprintPoint] = []) async throws -> UserFootprints {
        guard !userId.isEmpty else {
            throw FootprintsError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let footprints = try await performCreateFootprintsRequest(userId: userId, activityType: activityType, isFinished: isFinished, footPrints: footPrints)
            print("✅ 成功创建足迹记录: \(footprints.id)")
            isLoading = false
            return footprints
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 创建足迹记录失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    // MARK: 查询足迹记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    ///   - activityType: 活动类型
    /// - Returns: 足迹记录数组
    func queryFootprints(userId: String, startDate: Date? = nil, endDate: Date? = nil, activityType: ActivityType? = nil) async throws -> [UserFootprints] {
        guard !userId.isEmpty else {
            throw FootprintsError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let footprints = try await performQueryFootprintsRequest(userId: userId, startDate: startDate, endDate: endDate, activityType: activityType)
            print("✅ 成功查询足迹记录: \(footprints.count) 条")
            isLoading = false
            return footprints
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 查询足迹记录失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    // MARK: 更新足迹记录
    /// - Parameters:
    ///   - footprintId: 足迹记录ID
    ///   - footPrints: 新的足迹点（追加）
    ///   - activityType: 活动类型
    ///   - isFinished: 是否已完成
    /// - Returns: 更新后的足迹记录
    func updateFootprints(footprintId: String, footPrints: [FootprintPoint]? = nil, activityType: ActivityType? = nil, isFinished: Bool? = nil) async throws -> UserFootprints {
        guard !footprintId.isEmpty else {
            throw FootprintsError.missingFootprintId
        }

        isLoading = true
        errorMessage = nil

        do {
            let footprints = try await performUpdateFootprintsRequest(footprintId: footprintId, footPrints: footPrints, activityType: activityType, isFinished: isFinished)
            print("✅ 成功更新足迹记录: \(footprints.id)")
            isLoading = false
            return footprints
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 更新足迹记录失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    // MARK: 删除足迹记录
    /// - Parameter footprintId: 足迹记录ID
    /// - Returns: 删除是否成功
    func deleteFootprints(footprintId: String) async throws -> Bool {
        guard !footprintId.isEmpty else {
            throw FootprintsError.missingFootprintId
        }

        isLoading = true
        errorMessage = nil

        do {
            let success = try await performDeleteFootprintsRequest(footprintId: footprintId)
            print("✅ 成功删除足迹记录: \(footprintId)")
            isLoading = false
            return success
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 删除足迹记录失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    // MARK: 查询足迹记录详情
    /// - Parameter footprintId: 足迹记录ID
    /// - Returns: 足迹记录详情
    func queryFootprintsDetail(footprintId: String) async throws -> FootprintsDetailResponse {
        guard !footprintId.isEmpty else {
            throw FootprintsError.missingFootprintId
        }

        isLoading = true
        errorMessage = nil

        do {
            let detail = try await performQueryFootprintsDetailRequest(footprintId: footprintId)
            print("✅ 成功查询足迹记录详情: \(footprintId)")
            isLoading = false
            return detail
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 查询足迹记录详情失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    // MARK: 查询足迹统计
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    ///   - activityType: 活动类型
    /// - Returns: 足迹统计信息
    func queryFootprintsStats(userId: String, startDate: Date? = nil, endDate: Date? = nil, activityType: ActivityType? = nil) async throws -> FootprintsStatsResponse {
        guard !userId.isEmpty else {
            throw FootprintsError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let stats = try await performQueryFootprintsStatsRequest(userId: userId, startDate: startDate, endDate: endDate, activityType: activityType)
            print("✅ 成功查询足迹统计: \(userId)")
            isLoading = false
            return stats
        } catch {
            let footprintsError = mapError(error)
            errorMessage = footprintsError.localizedDescription
            print("❌ 查询足迹统计失败: \(footprintsError.localizedDescription)")
            isLoading = false
            throw footprintsError
        }
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Private Methods

    /// 设置日期编解码
    private func setupDateCoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 执行创建足迹记录请求
    private func performCreateFootprintsRequest(userId: String, activityType: ActivityType, isFinished: Bool, footPrints: [FootprintPoint]) async throws -> UserFootprints {
        guard let url = URL(string: baseURL + "footprints") else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 创建足迹记录: \(url.absoluteString)")
        print("🚶 用户ID: \(userId), 活动类型: \(activityType.displayName)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = CreateFootprintsRequest(userId: userId, activityType: activityType, isFinished: isFinished, footPrints: footPrints)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 创建足迹记录响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 创建足迹记录响应内容: \(responseString)")
        }

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingUserId
        case 404:
            throw FootprintsError.userNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let footprintsResponse = try jsonDecoder.decode(FootprintsResponse<UserFootprints>.self, from: data)

        if !footprintsResponse.success {
            throw FootprintsError.serverError(footprintsResponse.error ?? "创建足迹记录失败")
        }

        guard let footprints = footprintsResponse.data else {
            throw FootprintsError.serverError("响应数据为空")
        }

        return footprints
    }

    /// 执行查询足迹记录请求
    private func performQueryFootprintsRequest(userId: String, startDate: Date?, endDate: Date?, activityType: ActivityType?) async throws -> [UserFootprints] {
        var urlComponents = URLComponents(string: baseURL + "footprints")!
        var queryItems = [URLQueryItem(name: "userId", value: userId)]

        if let startDate = startDate {
            queryItems.append(URLQueryItem(name: "startDate", value: startDate.ISO8601Format()))
        }
        if let endDate = endDate {
            queryItems.append(URLQueryItem(name: "endDate", value: endDate.ISO8601Format()))
        }
        if let activityType = activityType {
            queryItems.append(URLQueryItem(name: "activityType", value: activityType.rawValue))
        }

        urlComponents.queryItems = queryItems

        guard let url = urlComponents.url else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 查询足迹记录: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询足迹记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingUserId
        case 404:
            throw FootprintsError.userNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let footprintsArray = try jsonDecoder.decode([UserFootprints].self, from: data)
        return footprintsArray
    }

    /// 执行更新足迹记录请求
    private func performUpdateFootprintsRequest(footprintId: String, footPrints: [FootprintPoint]?, activityType: ActivityType?, isFinished: Bool?) async throws -> UserFootprints {
        guard let url = URL(string: baseURL + "footprints") else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 更新足迹记录: \(url.absoluteString)")
        print("🚶 足迹ID: \(footprintId)")

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = UpdateFootprintsRequest(footprintId: footprintId, footPrints: footPrints, activityType: activityType, isFinished: isFinished)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 更新足迹记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingFootprintId
        case 404:
            throw FootprintsError.footprintNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let footprintsResponse = try jsonDecoder.decode(FootprintsResponse<UserFootprints>.self, from: data)

        if !footprintsResponse.success {
            throw FootprintsError.serverError(footprintsResponse.error ?? "更新足迹记录失败")
        }

        guard let footprints = footprintsResponse.data else {
            throw FootprintsError.serverError("响应数据为空")
        }

        return footprints
    }

    /// 执行删除足迹记录请求
    private func performDeleteFootprintsRequest(footprintId: String) async throws -> Bool {
        guard let url = URL(string: baseURL + "footprints") else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 删除足迹记录: \(url.absoluteString)")
        print("🚶 足迹ID: \(footprintId)")

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = DeleteFootprintsRequest(footprintId: footprintId)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 删除足迹记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingFootprintId
        case 404:
            throw FootprintsError.footprintNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let deleteResponse = try jsonDecoder.decode(FootprintsResponse<String>.self, from: data)
        return deleteResponse.success
    }

    /// 执行查询足迹记录详情请求
    private func performQueryFootprintsDetailRequest(footprintId: String) async throws -> FootprintsDetailResponse {
        guard let url = URL(string: baseURL + "footprints/detail") else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 查询足迹记录详情: \(url.absoluteString)")
        print("🚶 足迹ID: \(footprintId)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = FootprintsDetailRequest(footprintId: footprintId)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询足迹记录详情响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingFootprintId
        case 404:
            throw FootprintsError.footprintNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let detailResponse = try jsonDecoder.decode(FootprintsResponse<FootprintsDetailResponse>.self, from: data)

        if !detailResponse.success {
            throw FootprintsError.serverError(detailResponse.error ?? "查询足迹记录详情失败")
        }

        guard let detail = detailResponse.data else {
            throw FootprintsError.serverError("响应数据为空")
        }

        return detail
    }

    /// 执行查询足迹统计请求
    private func performQueryFootprintsStatsRequest(userId: String, startDate: Date?, endDate: Date?, activityType: ActivityType?) async throws -> FootprintsStatsResponse {
        guard let url = URL(string: baseURL + "footprints/stats") else {
            throw FootprintsError.serverError("无效的URL")
        }

        print("🚶 查询足迹统计: \(url.absoluteString)")
        print("🚶 用户ID: \(userId)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = FootprintsStatsRequest(userId: userId, startDate: startDate, endDate: endDate, activityType: activityType)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FootprintsError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询足迹统计响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw FootprintsError.missingUserId
        case 404:
            throw FootprintsError.userNotFound
        case 500:
            throw FootprintsError.serverError("服务器内部错误")
        default:
            throw FootprintsError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let statsResponse = try jsonDecoder.decode(FootprintsResponse<FootprintsStatsResponse>.self, from: data)

        if !statsResponse.success {
            throw FootprintsError.serverError(statsResponse.error ?? "查询足迹统计失败")
        }

        guard let stats = statsResponse.data else {
            throw FootprintsError.serverError("响应数据为空")
        }

        return stats
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> FootprintsError {
        if let footprintsError = error as? FootprintsError {
            return footprintsError
        }

        if error is URLError {
            return .networkError(error)
        }

        if error is DecodingError {
            return .decodingError(error)
        }

        return .unknown(error.localizedDescription)
    }
}
